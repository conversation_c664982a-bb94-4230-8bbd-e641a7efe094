'use client';

import type { Category } from '@/services/categories';
import type { Product, ProductDuration } from '@/services/products';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { Suspense, useEffect, useState } from 'react';
import { Autoplay, Pagination } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/react';
import ProductCard from '@/components/ProductCard';
import ShopLayout from '@/components/ShopLayout';
import { useCart } from '@/contexts/CartContext';
import { categoriesService } from '@/services/categories';
import { productService } from '@/services/products';
import 'swiper/css';
import 'swiper/css/pagination';
import 'swiper/css/navigation';

function ShopPageContent() {
  const t = useTranslations('Shop');
  const router = useRouter();
  const searchParams = useSearchParams();
  const { addToCart } = useCart();

  // State for categories and pagination
  const [categories, setCategories] = useState<Category[]>([]);
  const [categoryProducts, setCategoryProducts] = useState<Record<number, Product[]>>({});
  const [categoriesLoading, setCategoriesLoading] = useState<boolean>(true);
  const [loading, setLoading] = useState<boolean>(false);
  const [searchResults, setSearchResults] = useState<Product[]>([]);
  const [searchMeta, setSearchMeta] = useState<{ total: number; page: number; limit: number; totalPages: number }>({
    total: 0,
    page: 1,
    limit: 12,
    totalPages: 0,
  });

  // State for filters
  const [searchTerm, setSearchTerm] = useState<string>(searchParams?.get('search') || '');

  // Banner images
  const bannerImages = [
    '/assets/images/banner1.png',
    '/assets/images/banner2.png',
  ];

  // Load categories
  useEffect(() => {
    const fetchCategories = async () => {
      setCategoriesLoading(true);
      try {
        const data = await categoriesService.getCategories();
        // Sắp xếp danh mục để đưa các danh mục hot lên đầu
        const sortedCategories = [...data].sort((a, b) => {
          // Nếu a là hot và b không phải hot, a sẽ lên đầu
          if (a.is_hot && !b.is_hot) {
            return -1;
          }
          // Nếu b là hot và a không phải hot, b sẽ lên đầu
          if (!a.is_hot && b.is_hot) {
            return 1;
          }
          // Nếu cả hai đều hot hoặc không hot, sắp xếp theo sort
          return a.sort - b.sort;
        });
        setCategories(sortedCategories);
      } catch (error) {
        console.error('Error fetching categories:', error);
      } finally {
        setCategoriesLoading(false);
      }
    };

    fetchCategories();
  }, []);

  // Load products for each category only when there's no search term
  useEffect(() => {
    const fetchCategoryProducts = async () => {
      if (categories.length === 0 || searchTerm) {
        return;
      }

      const categoryProductsMap: Record<number, Product[]> = {};

      for (const category of categories) {
        try {
          const response = await productService.getProducts({
            page: 1,
            limit: 8,
            categories: [category.id.toString()],
          });

          // Products now include discounts and USDT conversion from the API
          categoryProductsMap[category.id] = response.data;
        } catch (error) {
          console.error(`Error fetching products for category ${category.id}:`, error);
          categoryProductsMap[category.id] = [];
        }
      }

      setCategoryProducts(categoryProductsMap);
    };

    fetchCategoryProducts();
  }, [categories, searchTerm]);

  // Load search results when search term changes
  useEffect(() => {
    // Check if there's a search parameter in the URL
    const searchFromUrl = searchParams?.get('search');

    if (searchFromUrl) {
      setSearchTerm(searchFromUrl);
      const fetchSearchResults = async () => {
        setLoading(true);
        try {
          const response = await productService.getProducts({
            page: searchMeta.page,
            limit: searchMeta.limit,
            search: searchFromUrl,
          });

          // Products now include discounts and USDT conversion from the API
          setSearchResults(response.data);
          setSearchMeta(response.meta);
        } catch (error) {
          console.error('Error fetching search results:', error);
          setSearchResults([]);
        } finally {
          setLoading(false);
        }
      };

      fetchSearchResults();
    } else {
      setSearchTerm('');
      setSearchResults([]);
    }
  }, [searchParams, searchMeta.page, searchMeta.limit]);

  // Handle category click
  const handleCategoryClick = (categoryId: number) => {
    router.push(`/categories/${categoryId}`);
  };

  // Handle adding product to cart
  const handleAddToCart = (product: Product, quantity: number = 1, selectedDuration: ProductDuration) => {
    // Add to cart with duration information
    addToCart(product, quantity, selectedDuration);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-10 overflow-hidden rounded-lg shadow-md">
        <Swiper
          modules={[Pagination, Autoplay]}
          pagination={{ clickable: true }}
          autoplay={{ delay: 5000, disableOnInteraction: false }}
          loop
          className="h-[250px] w-full md:h-[650px]"
        >
          {bannerImages.map((image, index) => (
            <SwiperSlide key={index}>
              <div className="relative size-full">
                <Image
                  src={image}
                  alt={`Banner ${index + 1}`}
                  fill
                  className="object-contain md:object-cover"
                  priority={index === 0}
                />
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>

      {/* Categories Section */}
      <div className="mb-8">
        <div className="mb-6 text-center">
          <h2 className="mb-2 text-3xl font-bold text-gray-900">{t('categories')}</h2>
          <div className="mx-auto h-1 w-20 rounded-full bg-gradient-to-r from-blue-500 to-purple-600"></div>
        </div>

        {categoriesLoading
          ? (
              <div className="flex h-40 items-center justify-center">
                <div className="relative">
                  <div className="size-16 animate-spin rounded-full border-4 border-gray-200"></div>
                  <div className="absolute left-0 top-0 size-16 animate-spin rounded-full border-4 border-transparent border-t-blue-500"></div>
                </div>
              </div>
            )
          : (
              <div className="flex flex-wrap justify-center gap-4">
                {categories.map(category => (
                  <button
                    key={category.id}
                    type="button"
                    className="group relative flex cursor-pointer flex-col items-center rounded-2xl p-6 transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl focus:outline-none focus:ring-4 focus:ring-blue-300 focus:ring-offset-2"
                    onClick={() => handleCategoryClick(category.id)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        handleCategoryClick(category.id);
                      }
                    }}
                  >
                    {/* Gradient background overlay */}
                    <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-blue-50 to-purple-50 opacity-0 transition-opacity duration-300 group-hover:opacity-100"></div>

                    {/* Icon container */}
                    <div className="relative z-10 mb-4 flex size-20 items-center justify-center rounded-2xl bg-gradient-to-br from-blue-500 to-purple-600 shadow-lg transition-all duration-300 group-hover:scale-110 group-hover:shadow-xl">
                      {category.img_url
                        ? (
                            <Image
                              src={category.img_url}
                              alt={category.name}
                              width={64}
                              height={64}
                              className="rounded-xl object-contain"
                            />
                          )
                        : (
                            <span className="text-2xl font-bold text-white">
                              {category.name.charAt(0).toUpperCase()}
                            </span>
                          )}
                    </div>

                    {/* Category name */}
                    <div className="relative z-10 flex flex-col items-center">
                      <span className="text-center text-sm font-semibold text-gray-700 transition-colors duration-300 group-hover:text-gray-900">
                        {category.name}
                      </span>
                    </div>
                  </button>
                ))}
              </div>
            )}
      </div>

      {/* Search Results Section - Display below categories when search term exists */}
      {searchTerm && (
        <div className="mb-10">
          <h2 className="mb-6 text-2xl font-bold">{t('searchResults', { searchTerm })}</h2>

          {loading
            ? (
                <div className="flex h-40 items-center justify-center">
                  <div className="size-12 animate-spin rounded-full border-y-2 border-blue-500"></div>
                </div>
              )
            : searchResults.length > 0
              ? (
                  <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
                    {searchResults.map(product => (
                      <div key={product.id} className="flex h-full">
                        <ProductCard
                          product={product}
                          onAddToCart={handleAddToCart}
                        />
                      </div>
                    ))}
                  </div>
                )
              : (
                  <div className="rounded-lg border border-gray-200 bg-white p-8 text-center">
                    <p className="text-lg text-gray-600">{t('noResults')}</p>
                  </div>
                )}
        </div>
      )}

      {/* Category Products Sections - Only show when there's no search term */}
      {!searchTerm && !categoriesLoading && categories.map((category) => {
        const categoryProds = categoryProducts[category.id] || [];
        if (categoryProds.length === 0) {
          return null;
        }

        return (
          <div key={category.id} className="mb-12">
            <div className="mb-4 flex items-center justify-between">
              <div className="flex items-center gap-2">
                <h2 className="text-xl font-bold">{category.name}</h2>
              </div>
              <Link href={`/categories/${category.id}`} className="rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700">
                {t('viewMore')}
              </Link>
            </div>

            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
              {categoryProds.slice(0, 4).map(product => (
                <div key={product.id} className="flex h-full">
                  <ProductCard
                    product={product}
                    onAddToCart={handleAddToCart}
                  />
                </div>
              ))}
            </div>
          </div>
        );
      })}
    </div>
  );
}

export default function ShopPage() {
  return (
    <ShopLayout>
      <Suspense fallback={<div className="p-4 text-center">Loading shop...</div>}>
        <ShopPageContent />
      </Suspense>
    </ShopLayout>
  );
}
